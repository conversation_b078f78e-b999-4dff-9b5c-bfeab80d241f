{"name": "taro_wx_demo", "version": "1.0.0", "private": true, "description": "智能婚恋服务系统", "templateInfo": {"name": "taro-ui", "typescript": false, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@tarojs/components": "4.1.6", "@tarojs/helper": "4.1.6", "@tarojs/plugin-framework-react": "4.1.6", "@tarojs/plugin-platform-alipay": "4.1.6", "@tarojs/plugin-platform-h5": "4.1.6", "@tarojs/plugin-platform-jd": "4.1.6", "@tarojs/plugin-platform-qq": "4.1.6", "@tarojs/plugin-platform-swan": "4.1.6", "@tarojs/plugin-platform-tt": "4.1.6", "@tarojs/plugin-platform-weapp": "4.1.6", "@tarojs/react": "4.1.6", "@tarojs/runtime": "4.1.6", "@tarojs/shared": "4.1.6", "@tarojs/taro": "4.1.6", "react": "^18.0.0", "react-dom": "^18.0.0", "taro-ui": "^3.2.1"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/preset-react": "^7.27.1", "@tarojs/cli": "4.1.6", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/vite-runner": "4.1.6", "@types/react": "^18.0.0", "@vitejs/plugin-react": "^4.1.0", "babel-preset-taro": "4.1.6", "eslint": "^8.12.0", "eslint-config-taro": "4.1.6", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "jest-environment-jsdom": "^29.5.0", "postcss": "^8.4.18", "react-refresh": "^0.11.0", "sass": "^1.60.0", "stylelint": "^14.4.0", "terser": "^5.16.8", "vite": "^4.2.0"}}